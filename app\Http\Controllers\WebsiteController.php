<?php

namespace App\Http\Controllers;

use App;
use App\Amenity;
use App\Booking;
use App\Category;
use App\CommonSetting;
use App\HelpCenter;
use App\Listing;
use App\Mail\ContactFormMail;
use App\Models\About;
use App\Models\ActivityLog;
use App\Models\AdminPolicy;
use App\Models\ContactInfo;
use App\Models\CurrencyConversionRate;
use App\Models\Faq;
use App\Models\ListingType;
use App\Models\ReservationDate;
use App\Models\ReviewReport;
use App\Models\User;
use App\Notifications\ReportNotification;
use App\Notifications\SuspendUserNotification;
use App\Report;
use App\Review;
use App\Role;
use App\Services\BookingService;
use App\Services\ListingService;
use App\WithdrawalRequest;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;

class WebsiteController extends Controller
{
    public function __construct(Request $request)
    {
        $this->middleware('checkCurrencySession');
    }

    public function index()
    {
        // session()->get('locale');
         $listing_types = ListingType::get();
        $amenities = Amenity::with("categories")->where("status", 1)->get();
        $categories = Category::with('tour_languages', "amenities", "listing_types")->where("status", 1)->get();
        return view('website.index', compact("categories", "amenities", "listing_types"));
    }
    function get_listing(Request $request, ListingService $listing)
    {
        if ($request->ajax()) {
            $search_data = false;

            if ($request->has('search_data') && !empty($request->search_data)) {
                // For search results, cache for 10 minutes
                $cacheKey = 'search_' . md5($request->search_data . (auth()->id() ?? 'guest'));

                $listings = Cache::remember($cacheKey, 10, function () use ($request, $listing) {
                    parse_str($request->input('search_data'), $searchDataArray);
                    $address = $searchDataArray['address'] ?? '';
                    $search_type = (!empty($address) && trim($address) !== '')
                        ? 'specific_location'
                        : 'current_location';
                    $current_lat = $searchDataArray['lat'] ?? null;
                    $current_lng = $searchDataArray['lng'] ?? null;

                    if (!$current_lat || !$current_lng) {
                        \Log::error('Latitude or Longitude is missing', $searchDataArray);
                        return collect();
                    }

                    $filteredListings = $listing->filter_listing($request->all());
                    $filteredListings = collect($filteredListings);
                    return $listing->apply_weighted_ranking($filteredListings, $search_type, $current_lat, $current_lng);
                });

                $search_data = true;
            } else {
                if ($request->listing_name) {
                    // Cache name search for 5 minutes
                    $cacheKey = 'name_search_' . md5($request->listing_name);
                    $listings = Cache::remember($cacheKey, 5, function () use ($request) {
                        $listings = Listing::where("name", "LIKE", "%$request->listing_name%")
                            ->with("detail:listing_id,basis_type,per_hour,per_day,child_price,adult_price", "user:id,name,avatar", "gallery_images", "wishlist")
                            ->active()
                            ->orderby("id", "DESC")
                            ->get();

                        $provider = User::has('listings')->where("name", "LIKE", "%{$request->listing_name}%")->pluck('id');
                        $provider_listing = Listing::whereIn("user_id", $provider)->active()->get();
                        return $listings->merge($provider_listing ?? []);
                    });
                } else {
                    // Cache default listings for 15 minutes
                    $cacheKey = 'default_listings_' . (auth()->id() ?? 'guest');
                    $listings = Cache::remember($cacheKey, 15, function () use ($listing) {
                        $result = $listing->get_listing();
                        return key_exists("data", $result) ? $result["data"] : $result;
                    });
                }
            }

            if (!empty($listings) && count($listings) > 0) {
                $listing = (string) view("website.template.home-card", compact("listings", "search_data"));
                return ["status" => true, "message" => "listings found", "data" => $listing, "json_data" => $listings];
            } else {
                return ["status" => false, "message" => "No listings found", "data" => ""];
            }
        } else {
            return ["status" => false, "message" => "Only ajax request accept", "data" => []];
        }
    }
    function listing_search_form(Request $request)
    {
        $request->validate([
            "listing_name" => "required"
        ]);
        return redirect()->route("/", ["listing_name" => $request->listing_name]);
    }
    function listing_detail(ListingService $listingService, $listing_id, $slug)
    {
        // Create cache key for this specific listing detail page
        $cacheKey = "listing_detail_{$listing_id}_{$slug}";
        $userCacheKey = auth()->check() ? "user_booking_check_" . auth()->id() . "_{$listing_id}" : null;

        // Cache the main listing data for 15 minutes
        $cachedData = Cache::remember($cacheKey, 15, function () use ($listingService, $slug, $listing_id) {
            $listing_service = $listingService->detail($slug, $listing_id);

            if ($listing_service["status"] != true) {
                return ['status' => false];
            }

            $listing = $listing_service["data"];
            $listing_detail = $listing->detail;
            $listing_id = $listing->id;

            // Cache reservation dates
            $reserve_dates = ReservationDate::where("listing_id", $listing_id)->pluck('date')->toArray();
            $reserve_dates_array = $reserve_dates;

            // cut off time logic
            if ($listing->detail->booking_close_time) {
                $now = now();
                $bookingCloseTime = Carbon::createFromFormat('h:i A', $listing->detail->booking_close_time);
                if ($now->greaterThan($bookingCloseTime)) {
                    $reserve_dates_array[] = now()->format('Y-m-d');
                }
            }

            $enable_dates = [];
            $availability_end = now();

            // Availability period calculation
            if (!empty($listing_detail->listing_availability_type) && $listing_detail->listing_availability_period > 0) {
                $availability_period = $listing_detail->listing_availability_period;
                if ($listing_detail->listing_availability_type === "week") {
                    $availability_end = now()->addWeeks($availability_period);
                } elseif ($listing_detail->listing_availability_type === "month") {
                    $availability_end = now()->addMonths($availability_period);
                }
            }

            // Cache booking dates calculation - this is the most expensive part
            $bookingCacheKey = "listing_booking_dates_{$listing_id}";
            $booking_dates_data = Cache::remember($bookingCacheKey, 10, function () use ($listing, $listing_detail) {
                $current_booking_dates = [];

                // Handle hourly bookings
                if ($listing->detail->basis_type == "Hourly") {
                    $bookedDates = $listing->booked_slots->groupBy('date');
                    $hourlyAvailability = $listing->hourly_availabilities->pluck('full_time')->toArray();
                    foreach ($bookedDates as $date => $bookings) {
                        $bookedSlots = $bookings->pluck('slot')->toArray();
                        if (count(array_intersect($hourlyAvailability, $bookedSlots)) === count($hourlyAvailability)) {
                            $current_booking_dates[] = $date;
                        }
                    }
                }

                // Handle tour bookings
                if($listing->detail->basis_type == "Tour"){
                    $requiredCapacity = $listing->detail->booking_capacity;
                    $bookings = $listing->bookings()->where("tour_type", "private")->get(['check_in', 'check_out']);
                    $guest_bookings = $listing->bookings()->get();
                    $uniqueDatesWithGuests = $guest_bookings->groupBy(function ($guest_booking) {
                        return \Carbon\Carbon::parse($guest_booking->check_in)->format('Y-m-d');
                    });
                    foreach ($uniqueDatesWithGuests as $date => $group) {
                        $guestSum = $group->sum('guest');
                        if ($guestSum >= $requiredCapacity) {
                            $current_booking_dates[] = $date;
                        }
                    }
                } else {
                    $bookings = $listing->bookings()->where("listing_basis", "Daily")->orWhere("tour_type", "private")->get(['check_in', 'check_out']);
                }

                // Process booking dates
                if (isset($bookings)) {
                    foreach ($bookings as $booking) {
                        $startDate = Carbon::parse($booking->check_in);
                        if($booking->check_in != $booking->check_out){
                            $endDate = Carbon::parse($booking->check_out)->subDay();
                        }else{
                            $endDate = Carbon::parse($booking->check_out);
                        }
                        $advanceDays = is_numeric($listing_detail->advance_booking_period) ? (int) $listing_detail->advance_booking_period : 0;
                        if($advanceDays > 0){
                            $startDate = Carbon::parse($booking->check_in)->subDays($advanceDays);
                            $endDate = Carbon::parse($booking->check_out)->addDays($advanceDays);
                        }
                        for ($date = $startDate; $date->lte($endDate); $date->addDay()) {
                            $current_booking_dates[] = $date->format('Y-m-d');
                        }
                    }
                }

                return array_values(array_unique($current_booking_dates));
            });

            $reserve_dates_array = array_merge($reserve_dates_array, $booking_dates_data);
            $enable_dates = array_values(array_diff($enable_dates, $reserve_dates_array));

            return [
                'status' => true,
                'listing' => $listing,
                'category' => $listing->category,
                'reserve_dates_array' => $reserve_dates_array,
                'enable_dates' => $enable_dates
            ];
        });

        // Handle cache miss or error
        if (!$cachedData['status']) {
            return view("errors.404");
        }

        // Extract cached data
        $listing = $cachedData['listing'];
        $category = $cachedData['category'];
        $reserve_dates_array = $cachedData['reserve_dates_array'];
        $enable_dates = $cachedData['enable_dates'];

        // Handle user-specific booking check (shorter cache for user-specific data)
        $booking_check = false;
        if (auth()->check() && $userCacheKey) {
            $booking_check = Cache::remember($userCacheKey, 5, function () use ($listing) {
                return Booking::where('user_id', auth()->id())
                    ->where('listing_id', $listing->id)
                    ->where('status', 0)
                    ->exists();
            });
        }

        if ($category) {
            return view("website.detail", compact("listing", "category", "reserve_dates_array", "booking_check", "enable_dates"));
        } else {
            return redirect("/")->with("message", "Category does not exist");
        }
    }

    function detailSlide(Request $request)
    {
        $listing = Listing::where('ids', $request->id)->with('detail')->first();
        $category = Category::where("id", $request->category)->first();

        $dates = [];
        $today = Carbon::today();

        if ($listing->detail->cancellation_policy == 'Strict') {
            $dateIncrement1 = $today->copy()->addDays(30);
            $dateIncrement2 = $dateIncrement1->copy()->addDays(14);
        } elseif ($listing->detail->cancellation_policy == 'Moderate') {
            $dateIncrement1 = $today->copy()->addDays(5);
            $dateIncrement2 = $dateIncrement1->copy()->addDays(2);
        } elseif ($listing->detail->cancellation_policy == 'Flexible') {
            $dateIncrement1 = $today->copy()->addDays(1);
            $dateIncrement2 = $dateIncrement1->copy()->addDays(1);
        }

        $dates = [
            'today' => $today->format('j M'),
            'increment1' => $dateIncrement1->format('j M'),
            'increment2' => $dateIncrement2->format('j M'),
        ];

        // "7bfabbe3-388f-4e11-b037-02297462c71b"
        return view("listing.listing.steps.accomodation.detail_iframe", compact('listing', 'category', 'dates'));
    }

    function listing_search(Request $request)
    {
        $listings = Listing::where("name", "LIKE", "%{$request->listing_name}%")->take(3)->get();
        $provider = User::has('listings')->where("name", "LIKE", "%{$request->listing_name}%")->pluck('id');
        $provider_listing = Listing::whereIn("user_id", $provider)->take(3)->get();
        $listings = $listings->merge($provider_listing ?? []);
        $data = (string) view("website.layout.listing-search", compact("listings"));
        return ["status" => true, "message" => "Listing found", "data" => $data];
    }
    function list_asset()
    {
        $user = auth()->user();
        if ($user->hasRole("customer")) {
            $role = Role::find(3);
            $user->roles()->first()->pivot->delete();
            $user->assignRole($role->name);
        }
        // return redirect("dashboard")->with("message", "You switched to the Service Provider dashboard");
        return redirect("dashboard");
    }
    function browse_listing()
    {
        $user = auth()->user();
        if ($user->hasRole("service")) {
            $role = Role::find(4);
            $user->roles()->first()->pivot->delete();
            $user->assignRole($role->name);
        }
        return redirect("/");
    }
    function locale($locale)
    {
        session()->put("locale", $locale);
        return redirect()->back();
    }
    function review_get(Request $request)
    {
        $listing = Listing::find($request->listing_id);
        if ($listing) {
            $reviews = Review::with("user", "listing")->whereListingId($listing->id)->orderBy("id", "DESC")->get();
            if (!$reviews->isEmpty()) {
                $data = (string) view("website.template.user_review", compact("reviews"));
                return ["status" => true, "message" => "Review found", "data" => [
                    "review_count" => count($reviews),
                    "review_data" => $data
                ]];
            } else {
                return ["status" => false, "message" => "Review not found", "data" => []];
            }
        } else {
            return ["status" => false, "message" => "Listing not found", "data" => []];
        }
    }
    public function detail()
    {
        return view('website.detail');
    }
    
    public function detailMedical()
    {
        return view('website.detailMedical');
    }
    public function contactUs()
    {
        $common_setting = CommonSetting::first();
        return view('website.contact_us', compact("common_setting"));
    }
    public function about()
    {
        $about = About::with("contents")->first();
        return view('website.about', compact("about"));
    }
    public function bookings()
    {
        return view('website.bookings');
    }
    public function privacy()
    {
        $privacy_policy = AdminPolicy::where("type", "privacy_policy")->first();
        return view('website.privacy', compact("privacy_policy"));
    }
    function supplier_aggreement()
    {
        $supplier_aggreement = AdminPolicy::where("type", "supplier_aggreement")->first();
        return view('website.supplier_aggreement', compact("supplier_aggreement"));
    }
    public function terms()
    {
        $term_condition = AdminPolicy::where("type", "term_condition")->first();
        return view('website.terms', compact("term_condition"));
    }

    public function messenger()
    {
        return view('website.messenger');
    }

    public function faq(Request $request)
    {
        if ($request->search) {
            $searchTerm = trim($request->search);
            // Search in both title and description fields with improved matching
            $faqs = Faq::where(function ($query) use ($searchTerm) {
                $query->where("title", "LIKE", "%$searchTerm%")
                      ->orWhere("description", "LIKE", "%$searchTerm%");
            })->get();
            // If no results found with exact search, try word-by-word search
            if ($faqs->isEmpty() && strlen($searchTerm) > 2) {
                $words = explode(' ', $searchTerm);
                $faqs = Faq::where(function ($query) use ($words) {
                    foreach ($words as $word) {
                        $word = trim($word);
                        if (strlen($word) > 2) { // Only search for words longer than 2 characters
                            $query->orWhere("title", "LIKE", "%$word%")
                                  ->orWhere("description", "LIKE", "%$word%");
                        }
                    }
                })->get();
            }
        } else {
            $faqs = Faq::get();
        }
        return view('website.faq', compact("faqs"));
    }
    function faq_detail($slug)
    {
        $faq = Faq::where("slug", $slug)->firstOrFail();
        return view('website.faq_detail', compact("faq"));
    }
    public function get_past_booking()
    {
        $past_bookings = BookingService::past_booking();
        if ($past_bookings['status'] == true) {
            $bookings = $past_bookings['data'];
            $booking = (string) view("website.template.booking-card", compact("bookings"));
            return ["status" => true, "message" => "data found", "data" => $booking];
        } else {
            return ["status" => false, "message" => $past_bookings["message"], "data" => []];
        }
    }
    function get_current_booking()
    {
        $current_booking = BookingService::current_booking();
        if ($current_booking["status"] == true) {
            $bookings = $current_booking['data'];
            $booking = (string) view("website.template.booking-card", compact("bookings"));
            return ["status" => true, "message" => "Data found", "data" => $booking];
        } else {
            return ["status" => false, "message" => $current_booking["message"], "data" => []];
        }
    }
    public function webaccountSetting()
    {
        return view('website.webaccount_setting');
    }
    public function inboxChat($provider_id)
    {
        $user = User::findOrFail($provider_id);
        if ($user) {
            return view('website.inbox', compact("user"));
        }
    }
    public function cart()
    {
        return view('website.cart');
    }
    public function listings()
    {
        $listings = Listing::with("category", "user")->get();
        return view('dashboard.listings', compact("listings"));
    }
    function checkout()
    {
        return view("website.checkout");
    }
    public function userManagement(Request $req)
    {
        // dd($req);
        $customers  = User::with("listings")->whereHas('roles', function ($query) {
            $query->where('name', 'customer');
        })->paginate(10, pageName: 'customers')->fragment('customers');
        $service_users = User::with("listings")->whereHas('roles',  function ($query) {
            $query->where('name', 'service');
        })->paginate(10, pageName: 'service_provider')->fragment('service');
        $sub_admins = User::with("listings")->whereHas('roles', function ($query) {
            $query->where('name', 'sub_admin');
        })->paginate(10, pageName: 'sub_admins')->fragment('admins');
        return view('dashboard.user_management', compact("customers", "service_users", "sub_admins"));
    }
    function user_detail($id)
    {
        $user = User::find($id);
        if ($user) {
            return ["status" => true, "message" => "User Found", "data" => $user, "role" => $user->hasRole("user") ? 2 : 5];
            // return api_response(true, "User Found", $user);
        } else {
            return api_response(false, "User not Found", null);
        }
    }
    function admin_user_update(Request $request)
    {
        $validateUser = Validator::make(
            $request->all(),
            [
                'id' => 'required',
                'first_name' => 'required|alpha',
                'last_name' => 'required|alpha',
                // 'email' => "required|email|unique:users,email|regex:/(.+)@(.+)\.(.+)/i",
                'phone' => 'required|numeric',
                // 'password' => 'required|min:8|confirmed',
            ]
        );
        if ($validateUser->fails()) {
            return api_response(false, $validateUser->errors()->first(), null);
        }
        $user = User::find($request->id);
        $user->first_name = $request->first_name;
        $user->last_name = $request->last_name;
        $user->name = $request->first_name . " " . $request->last_name;
        // $user->email = $request->email;
        // $user->password = Hash::make($request->password);
        $user->save();
        return api_response(true, "User Updated Successfully");
    }

    public function customerDetails($id)
    {
        $user = User::with("profile")->where("ids", $id)->first();
        if(!$user){
            return back()->with(["type" => "error", "message" => "Customer not found", "title" => "Error"]);
        }
        $active_booking = Booking::where("user_id", $user->id)->where("status", 0)->count();
        $reviews = Review::where("user_id", $user->id)->get();
        $total_expenditure = Booking::where("user_id", $user->id)->sum("total_amount");
        $bookings = Booking::where("user_id", $user->id)->get();
        return view('dashboard.customer_details', compact("user", "active_booking", "reviews", "total_expenditure", "bookings"));
    }
    public function adminDetails($id)
    {
        $user = User::with("profile")->where("ids", $id)->first();
        if(!$user){
            return back()->with(["type" => "error", "message" => "Admin not found", "title" => "Error"]);
        }
        $activity_logs = ActivityLog::where("causer_id", $id)->get();
        return view('dashboard.admin', compact("user", "activity_logs"));
    }
    public function serviceProvider($id)
    {
        $user = User::with("profile")->where("ids", $id)->first();
        if(!$user){
            return back()->with(["type" => "error", "message" => "Service Provider not found", "title" => "Error"]);
        }
        $active_booking = Booking::where("provider_id", $user->id)->where("status", 0)->count();
        $reviews = Review::where("provider_id", $user->id)->get();
        $revenue = Booking::where("provider_id", $user->id)->sum("total_amount");
        $outstanding_amount = WithdrawalRequest::where("user_id", $user->id)->where("status", 0)->sum("amount");
        $bookings = Booking::where("provider_id", $user->id)->get();
        return view('dashboard.service_provider', compact("user", "active_booking", "reviews", "revenue", "outstanding_amount", "bookings"));
    }
    function service_provider_active_bookings($id, BookingService $bookingService)
    {
        $user = User::where("id", decrypt($id))->first();
        if (!$user) {
            return api_response(false, "User not found");
        }
        $active_bookings = $bookingService->activeBookings('provider', $user->id);
        $active_booking_table = (string) view("includes.customer-suspend-modal-table", compact('active_bookings', "user"));
        return api_response(true, "Data found", $active_booking_table);
    }

    // Testing Email template function starts from here
    public function testingEmailTemp()
    {
        $user = auth()->user();
        $common_setting = CommonSetting::first();
        $listing = Listing::where('id', 9)->with('detail')->first();
        return view('email-templates.testing-email-temp', compact('user', 'listing', 'common_setting'));
    }
    public function cancellationPolicyTimeline(Request $request)
    {
        $policyType = $request->policyType;
        $startDate = Carbon::parse($request->startDate);
        $today = Carbon::today();
        $refundPercentage = 0;
        // $message = '';
        $dates = [];
        $daysDifference = $today->diffInDays($startDate);
        if ($policyType == 'Flexible') {
            $hoursDifference = $today->diffInHours($startDate);
            $dates['flexible_cutoff'] = $startDate->copy()->subHours(24)->format('j M');
            if ($hoursDifference >= 24) {
                $refundPercentage = 100;
            } else {
                $refundPercentage = 0;
            }
        } elseif ($policyType == 'Moderate') {
            $dates['moderate_cutoff_full'] = $startDate->copy()->subDays(5)->format('j M');
            $dates['moderate_cutoff_half'] = $startDate->copy()->subDays(2)->format('j M');
            if ($today->diffInDays($startDate) <= 2) {
                $refundPercentage = 0;
            } elseif ($daysDifference > 5) {
                $refundPercentage = 100;
            } elseif ($daysDifference >= 2) {
                $refundPercentage = 50;
            }
        } elseif ($policyType == 'Strict') {
            $dates['strict_cutoff_full'] = $startDate->copy()->subDays(30)->format('j M');
            $dates['strict_cutoff_half'] = $startDate->copy()->subDays(14)->format('j M');
            if ($today->diffInDays($startDate) > 30) {
                $refundPercentage = 100;
            } elseif ($today->diffInDays($startDate) >= 14) {
                $refundPercentage = 50;
            } else {
                $refundPercentage = 0;
            }
        }
        return view('website.cancellation-policy-timeline', compact('policyType', 'startDate', 'refundPercentage', 'dates'));
    }
    function calculate_detail_daily(Request $request)
    {
        $listing = Listing::with("seasons")->where("ids", $request->listing_id)->firstOrFail();
        $start_date = $request->start_date ?? $request->end_date;
        $end_date = $request->end_date ?? $request->start_date;
        $guest = $request->guest ?? 1;
        $listingPrice = season_price($start_date, $listing->base_price, $listing->seasons);
        $checkIn = Carbon::parse($start_date);
        $checkOut = Carbon::parse($end_date);
        if($listing->category_id == 4){
            $totalDays = $checkIn->diffInDays($checkOut);
        }else{
            $totalDays = $checkIn->diffInDays($checkOut) + 1;
        }
        $conversion_rate = session('conversion_rate', 1);
        $listing_price = $listingPrice * $conversion_rate;
        $base_price = $listing->base_price * $conversion_rate;
        $currency = session('currency', 'COP');
        $listing_total = $listing_price * $totalDays;
        $weekly_discount_percentage = $listing->discount->weekly_discount ?? 0;
        $monthly_discount_percentage = $listing->discount->monthly_discount ?? 0;
        $weekly_monthly_discount = weeklyMonthlyDiscount($totalDays, $listing_total, $listing->discount);
        $new_listing_discount = newListingDiscount($listing_total, $listing->bookings()->count(), $listing->discount);
        $page = (string) view("website.template.daily-card", compact(
            "listing",
            'start_date',
            'end_date',
            'guest',
            "listingPrice",
            "totalDays",
            "listing_price",
            "base_price",
            "currency",
            'conversion_rate',
            "listing_total",
            "weekly_discount_percentage",
            "monthly_discount_percentage",
            "weekly_monthly_discount",
            "new_listing_discount"
        ));
        return api_response(true, "Calculated data", $page);
    }
    function calculate_detail_hourly(Request $request)
    {
        $listing = Listing::with("seasons")->where("ids", $request->listing_id)->firstOrFail();
        $start_date = $request->start_date;
        $time_slots = $request->time_slots ?? [];
        $listingPrice = season_price($start_date, $listing->base_price, $listing->seasons);
        $conversion_rate = session('conversion_rate', 1);
        $listing_price = $listingPrice * session('conversion_rate', 1);
        $base_price = $listing->base_price * session('conversion_rate', 1);
        $currency = session('currency', 'COP');
        $new_listing_discount = newListingDiscount($listingPrice, $listing->bookings()->count(), $listing->discount);
        $bookedSlots = $listing->booked_slots()->where('date', $start_date)->pluck('slot')->toArray();

        $today = today()->format('Y-m-d');
        $current_time = now()->format('H:i');
        $passedSlots = [];
        if ($today == $start_date) {
            foreach ($listing->hourly_availabilities as $slot) {
                $slotEndTime = strtotime($slot['end_time']);
                $slotStartTime = strtotime($slot['start_time']);
                $nowTime = strtotime($current_time);
                if ($slotEndTime <= $nowTime && $slotStartTime < $slotEndTime) {
                    $passedSlots[] = $slot['full_time'];
                }
            }
        }
        $page = (string) view("website.template.hourly-card", compact(
            "listing",
            'start_date',
            "currency",
            "listing_price",
            'time_slots',
            "base_price",
            "conversion_rate",
            "new_listing_discount",
            "bookedSlots",
            "passedSlots"
        ));
        return api_response(true, "Calculated hour data", [
            "page" => $page,
            "listing_price" => $listing_price,
            "new_listing_discount" => $new_listing_discount
        ]);
    }
    function calculate_detail_tour(Request $request){
        $listing = Listing::with("seasons")->where("ids", $request->listing_id)->firstOrFail();
        $currency = session('currency', 'COP');
        $adult_number = $request->number_adult ?? 1;
        $child_number = $request->number_child ?? 0;
        $tour_type = $request->tour_type;
        $start_date = $request->start_date;
        $adult_price_base = season_price($start_date, $listing->detail->adult_price, $listing->seasons);
        $child_price_base = season_price($start_date, $listing->detail->child_price, $listing->seasons);
        $conversion_rate = session('conversion_rate', 1);
        $private_price_base = season_price($start_date, $listing->detail->private_booking_price, $listing->seasons);

        // remaining capacity
        $booking_capacity = $listing->detail->booking_capacity ?? 0;
        $active_booking = Booking::where("listing_basis", "Tour")->where("check_in", $start_date)->where("listing_id", $listing->id)->where("status", 0)->sum("guest");
        $remaining_capacity = $booking_capacity - $active_booking;
        // remaining capacity end

        if($tour_type == "guests" || $active_booking > 0){
            $total_price = ($adult_price_base * $adult_number) + ($child_price_base * $child_number);
        }else{
            $total_price = $private_price_base;
        }
        $new_listing_discount = newListingDiscount($total_price, $listing->bookings()->count(), $listing->discount);
        $page = (string) view("website.template.tour-card", compact(
            "listing",
            'active_booking',
            'start_date',
            "remaining_capacity",
            "adult_price_base",
            "child_price_base",
            "private_price_base",
            "tour_type",
            "currency",
            "adult_number",
            "child_number",
            "total_price",
            "conversion_rate",
            "new_listing_discount",
        ));
        return api_response(true, "Calculated tour data", [
            "page" => $page,
            "adult_price" => floor($adult_price_base * $conversion_rate),
            "child_price" => floor($child_price_base * $conversion_rate),
            "new_listing_discount" => $new_listing_discount
        ]);
    }
    function report_form(Request $request)
    {
        $request->validate([
            "booking_id" => "required",
            "subject" => "required",
            "description" => "required",
        ]);
        $booking = Booking::where("ids", $request->booking_id)->where("user_id", auth()->id())->first();
        if ($booking) {
            $report = new Report();
            $report->subject = $request->subject;
            $report->description = $request->description;
            $report->booking_id = $booking->id;
            $report->listing_id = $booking->listing_id;
            $report->provider_id = $booking->provider_id;
            $report->user_id = auth()->id();
            $report->save();
            $booking->provider->notify(new ReportNotification(auth()->user()->name . " reported the listing " . $booking->listing->name));
            admins_notification(new ReportNotification(auth()->user()->name . " reported the listing " . $booking->listing->name));
            return back()->with(["message" => "Report Added Successfully", "type" => "success", "title" => "Report"]);
        } else {
            return back()->with(["message" => "Booking not found", "type" => "error", "title" => "Report"]);
        }
    }
    function report_review(Request $request){
        $request->validate([
            "subject" => "required",
            "listing_id" => "required|exists:listings,ids",
            "other_reason" => "required_if:subject,Other",
        ]);

        $listing = Listing::where("ids", $request->listing_id)->first();
        $review_report = ReviewReport::where("user_id", auth()->id())->where("review_id", $request->review_id)->where("type", $request->report_type)->where("listing_id", $listing->id)->first();
        if($review_report){
            return back()->with(["message" => "You have already reported this review", "type" => "error", "title" => "Review Report"]);
        }
        $review_report = new ReviewReport();
        $review_report->subject = $request->subject;
        if($request->subject == "Other"){
            $review_report->other_reason = $request->other_reason;
        }
        $review_report->type = $request->report_type;
        $review_report->review_id = $request->review_id;
        $review_report->listing_id = $listing->id;
        $review_report->user_id = auth()->id();
        $review_report->save();
        return back()->with(["message" => "", "type" => "success", "title" => "Review Reported"]);
    }
    function helpCenter(Request $request)
    {
        if ($request->search) {
            $searchTerm = trim($request->search);

            // Search in help center titles and descriptions, and also in their FAQs
            $help_centers = HelpCenter::with("faqs")
                ->where(function ($query) use ($searchTerm) {
                    $query->where("title", "LIKE", "%$searchTerm%")
                          ->orWhere("description", "LIKE", "%$searchTerm%")
                          ->orWhereHas('faqs', function ($faqQuery) use ($searchTerm) {
                              $faqQuery->where("title", "LIKE", "%$searchTerm%")
                                       ->orWhere("description", "LIKE", "%$searchTerm%");
                          });
                })
                ->paginate();

            // If no results found with exact search, try word-by-word search
            if ($help_centers->isEmpty() && strlen($searchTerm) > 2) {
                $words = explode(' ', $searchTerm);
                $help_centers = HelpCenter::with("faqs")
                    ->where(function ($query) use ($words) {
                        foreach ($words as $word) {
                            $word = trim($word);
                            if (strlen($word) > 2) {
                                $query->orWhere("title", "LIKE", "%$word%")
                                      ->orWhere("description", "LIKE", "%$word%")
                                      ->orWhereHas('faqs', function ($faqQuery) use ($word) {
                                          $faqQuery->where("title", "LIKE", "%$word%")
                                                   ->orWhere("description", "LIKE", "%$word%");
                                      });
                            }
                        }
                    })
                    ->paginate();
            }
        } else {
            $help_centers = HelpCenter::with("faqs")->paginate();
        }

        return view('website.help_center', compact("help_centers"));
    }
    function help_center_detail($slug)
    {
        $help_center = HelpCenter::with("faqs")->where("slug", $slug)->firstOrFail();
        return view('website.help_center_detail', compact("help_center"));
    }
    public function googleTranslateChange(Request $request)
    {
        app()->setLocale($request->lang);

        session()->put('locale', $request->lang);

        return redirect()->back();
    }
    public function proxy_google(Request $request)
    {
        $url = 'https://maps.googleapis.com/maps/api/js';
        $params = $request->all();
        $params['key'] = google_map_key();

        $response = Http::get($url, $params);

        return response($response->body(), $response->status())
            ->header('Content-Type', $response->header('Content-Type'));
    }
    public function translate(Request $request)
    {
        $text = $request->input('text');
        $targetLang = $request->input('lang', 'es'); // default to Spanish

        $response = Http::get('https://translate.googleapis.com/translate_a/single', [
            'client' => 'gtx',
            'sl' => 'auto',
            'tl' => $targetLang,
            'dt' => 't',
            'q' => $text
        ]);

        return response()->json($response->json());
    }
    function cache_clear()
    {
        $exitCodeConfig = Artisan::call('storage:link');
        $exitCodeConfig = Artisan::call('route:clear');
        $exitCodeCache = Artisan::call('cache:clear');
        $exitCodeUpdate = Artisan::call('optimize:clear');
        $exitCodeView = Artisan::call('view:clear');
        // $exitCodePermissionCache = Artisan::call('permission:cache-reset');
        //$exitCodePermissionCache = Artisan::call('cache:forget laravelspatie.permission.cache');
        return '<div style="text-align:center;"> <h1 style="text-align:center;">Cache and Config and permission cache are cleared.</h1><h4><a href="/">Go to home</a></h4></div>';
    }

    /**
     * Clear cache for a specific listing detail page
     * This should be called when listing data is updated
     */
    public function clearListingDetailCache($listing_id, $slug = null)
    {
        // Clear main listing detail cache
        if ($slug) {
            Cache::forget("listing_detail_{$listing_id}_{$slug}");
        }

        // Clear booking dates cache
        Cache::forget("listing_booking_dates_{$listing_id}");

        // Clear all listing detail caches for this listing (in case slug changed)
        $pattern = "listing_detail_{$listing_id}_*";

        // Note: For Redis cache, you could use more advanced pattern matching
        // For file cache, we'll just clear the specific keys we know about

        return true;
    }
    //current location Func
    public function getUserInfo(Request $request)
    {
        try {
            $userIp = $request->ip() ?? '**************';
            $response = Http::get("https://ipinfo.io/{$userIp}", [
                'token' => '72d8995d8d7080',
            ]);
            if ($response->successful()) {
                $data = $response->json();
                $location = $data['loc'] ?? 'Not available';
                $country = $data['country'] ?? 'CO';
                $currencyResponse = Http::get("https://restcountries.com/v3.1/alpha/{$country}");
                $currency = 'COP';
                if ($currencyResponse->successful()) {
                    $currencyData = $currencyResponse->json();
                    $currency = $currencyData[0]['currencies']
                        ? array_keys($currencyData[0]['currencies'])[0]
                        : 'COP';
                }

                $this->currency($currency);
            } else {
                session()->put('conversion_rate', 1);
                session()->put('currency', 'COP');
            }
            session()->put('user_ip', $userIp);
        } catch (\Exception $e) {
            return false;
        }
    }
    //currency switch Func
    public function currency($to = 'USD')
    {
        try {
            $rates =  CurrencyConversionRate::where('target_currency', $to)->first();
            $rate = $rates->rate ?? 1; // Fallback to 1 if rate is not found
            session()->put('conversion_rate', $rate);
            session()->put('currency', $to);
        } catch (\Exception $e) {
            session()->put('conversion_rate', 1);
            session()->put('currency', 'COP');
        }
        return redirect()->back();
    }
    // all conversion rate list against cop Func For Cron
    public function updateConversionRatesList()
    {
        try {
            $apiKey = '985867fe7977be6a81ed0bc3cc1bf1fa'; // Replace with your API key
            $baseCurrency = 'COP';
            $currencies = [
                'USD',
                'GBP',
                'EUR',
                'AED',
                'AFN',
                'ALL',
                'AMD',
                'ANG',
                'AOA',
                'ARS',
                'AUD',
                'AWG',
                'AZN',
                'BAM',
                'BBD',
                'BDT',
                'BGN',
                'BHD',
                'BIF',
                'BMD',
                'BND',
                'BOB',
                'BRL',
                'BSD',
                'BWP',
                'BYN',
                'BZD',
                'CAD',
                'CHF',
                'CNY',
                'COP',
                'EGP',
                'INR',
                'JPY',
                'PKR',
                'RUB',
                'SAR',
                'SEK',
                'TRY',
                'ZAR',
                'ZMW'
            ];
            $url = "http://api.currencylayer.com/live?access_key=$apiKey&currencies=" . implode(',', $currencies) . "&source=$baseCurrency&format=1";
            $response = Http::get($url);
            if ($response->successful()) {
                $rates = $response->json();
                $conversionRates = [];
                $apiTimestamp = $rates['timestamp'];
                CurrencyConversionRate::where('base_currency', $baseCurrency)->update(['is_default' => 0]);
                foreach ($currencies as $currency) {
                    $key = $baseCurrency . $currency;
                    //$conversionRates[$key] = round($rates['quotes'][$key] ?? 1, 10); // Round the rate to 10 decimal places
                    $conversionRates[$key] = $rates['quotes'][$key] ?? 1;
                    $isDefault = ($currency === 'USD') ? 1 : 0;
                    CurrencyConversionRate::updateOrCreate(
                        ['base_currency' => $baseCurrency, 'target_currency' => $currency],
                        [
                            'rate' => $conversionRates[$key],
                            'api_timestamp' => \Carbon\Carbon::createFromTimestamp($apiTimestamp)->toDateTimeString(),
                            'is_default' => $isDefault
                        ]
                    );
                }
                return response()->json(['message' => 'Currency conversion rate List updated successfully in database.']);
            } else {
                return response()->json(['error' => 'Failed to fetch currency rates.'], 500);
            }
        } catch (\Exception $e) {
            return response()->json(['error' => 'Error updating conversion rates.'], 500);
        }
    }
    function redirectToListing($url_code){
        $listing = Listing::where("url_code", $url_code)->firstOrFail();
        return redirect()->route("detail", ["listing_id" => $listing->ids, "slug" => $listing->slug]);
    }

    /**
     * Clear listing caches - simple version
     */
    public function clearListingCaches()
    {
        try {
            // Clear all cache - simple and safe
            Cache::flush();

            return response()->json([
                'message' => 'All caches cleared successfully',
                'timestamp' => now()->toDateTimeString()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to clear caches: ' . $e->getMessage(),
                'timestamp' => now()->toDateTimeString()
            ], 500);
        }
    }
}
