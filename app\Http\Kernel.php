<?php

namespace App\Http;

use App\Http\Middleware\LayoutMiddleware;
use Illuminate\Foundation\Http\Kernel as HttpKernel;

class <PERSON>el extends HttpKernel
{
    /**
     * The application's global HTTP middleware stack.
     *
     * These middleware are run during every request to your application.
     *
     * @var array
     */
    protected $middleware = [
        \Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode::class,
        \Illuminate\Foundation\Http\Middleware\ValidatePostSize::class,
        \App\Http\Middleware\TrimStrings::class,
        \Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull::class,
        \App\Http\Middleware\TrustProxies::class,
        \App\Http\Middleware\SecurityHeaders::class,
    ];

    /**
     * The application's route middleware groups.
     *
     * @var array
     */
    protected $middlewareGroups = [
        'web' => [
            \App\Http\Middleware\EncryptCookies::class,
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\Session\Middleware\StartSession::class,
            // \Illuminate\Session\Middleware\AuthenticateSession::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
            \App\Http\Middleware\VerifyCsrfToken::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            LayoutMiddleware::class,
            \App\Http\Middleware\TrackLastActiveAt::class,
            \App\Http\Middleware\LanguageMiddleware::class,
            \App\Http\Middleware\GoogleTranslate::class, // REGISTER MIDDLEWARE HERE
            \App\Http\Middleware\LogUserStepMiddleware::class, // REGISTER MIDDLEWARE HERE
            \App\Http\Middleware\CheckSessionStatus::class, // CHECK SESSION STATUS
            \App\Http\Middleware\HtmlMinifier::class,
        ],

        'api' => [
            \Laravel\Sanctum\Http\Middleware\EnsureFrontendRequestsAreStateful::class,
            'throttle:60,1',
            'bindings',
            'json.request' => \App\Http\Middleware\EnsureJsonRequest::class,
            \App\Http\Middleware\LogUserStepMiddleware::class, // REGISTER MIDDLEWARE HERE
        ],
    ];

    /**
     * The application's route middleware.
     *
     * These middleware may be assigned to groups or used individually.
     *
     * @var array
     */
    protected $routeMiddleware = [
        'auth' => \Illuminate\Auth\Middleware\Authenticate::class,
        'auth.basic' => \Illuminate\Auth\Middleware\AuthenticateWithBasicAuth::class,
        'bindings' => \Illuminate\Routing\Middleware\SubstituteBindings::class,
        'cache.headers' => \Illuminate\Http\Middleware\SetCacheHeaders::class,
        'can' => \Illuminate\Auth\Middleware\Authorize::class,
        'guest' => \App\Http\Middleware\RedirectIfAuthenticated::class,
        'signed' => \Illuminate\Routing\Middleware\ValidateSignature::class,
        'throttle' => \Illuminate\Routing\Middleware\ThrottleRequests::class,
        //        'role' => \Spatie\Permission\Middlewares\RoleMiddleware::class,
        //        'role' => \App\Http\Middleware\Role::class,
        'roles' => \App\Http\Middleware\CheckRole::class,
        //        'permission' => \Spatie\Permission\Middlewares\PermissionMiddleware::class,
        "last_active" => \App\Http\Middleware\TrackLastActiveAt::class,
        "kyc_verification" => \App\Http\Middleware\VerificationCheckMiddleware::class,
        "user_role" => \App\Http\Middleware\UserRoleMiddleware::class,
        "ajax" => \App\Http\Middleware\AjaxRequestMiddleware::class,
        'cacheControl' => \App\Http\Middleware\CacheControl::class,
        'checkCurrencySession' => \App\Http\Middleware\CheckCurrencySession::class,
        'listingConsent' => \App\Http\Middleware\ListingConsentMiddleware::class,
        'checkSessionStatus' => \App\Http\Middleware\CheckSessionStatus::class,
    ];
}
