<?php

namespace App;

use App\Models\BillingAddress;
use App\Models\BookingDetail;
use App\Models\BookingDiscount;
use App\Models\BookingHourSlot;
use App\Models\BookingTourDuration;
use App\Traits\HasUuid;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Illuminate\Support\Facades\Cache;

class Booking extends Model
{
    use SoftDeletes, HasUuid, LogsActivity;

    // Optional: customize the logged attributes
    protected static $logAttributes = ['*'];
    protected static $logName = 'booking';
    protected static $logOnlyDirty = true;

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'bookings';

    /**
     * The database primary key value.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * Attributes that should be mass-assignable.
     *
     * @var array
     */
    protected $fillable = ['user_id', 'listing_id', 'total_days', 'check_in', 'check_out', 'guest', 'listing_basis', 'duration_discount_name', 'duration_discount_percent', 'duration_discount_amount', 'listing_price', 'total_amount', 'total_usd_amount', 'sub_total', 'status', 'payment_method'];

    /**
     * Boot method to handle cache clearing when booking is updated
     */
    protected static function boot()
    {
        parent::boot();

        // Clear cache when booking is created, updated, or deleted
        static::created(function ($booking) {
            self::clearBookingCache($booking);
        });

        static::updated(function ($booking) {
            self::clearBookingCache($booking);
        });

        static::deleted(function ($booking) {
            self::clearBookingCache($booking);
        });
    }

    /**
     * Clear listing-related caches when booking changes
     */
    protected static function clearBookingCache($booking)
    {
        if ($booking->listing_id) {
            // Clear booking dates cache for the listing
            Cache::forget("listing_booking_dates_{$booking->listing_id}");

            // Clear listing detail cache (we don't know the slug, so we'll clear when listing is accessed next)
            // The listing detail cache will be refreshed on next access
        }
    }

    function billing_address()
    {
        return $this->hasOne(BillingAddress::class);
    }
    function listing()
    {
        return $this->belongsTo(Listing::class, "listing_id")->with("user");
    }
    function customer()
    {
        return $this->belongsTo(User::class, "user_id");
    }
    function provider()
    {
        return $this->belongsTo(User::class, "provider_id");
    }
    function statusName()
    {
        return $this->belongsTo(Status::class, "status")->select();
    }
    function review()
    {
        return $this->hasOne(Review::class)->where("user_id", auth()->id());
    }
    function reviews()
    {
        return $this->hasMany(Review::class);
    }
    function discounts()
    {
        return $this->hasMany(BookingDiscount::class);
    }
    function detail()
    {
        return $this->hasOne(BookingDetail::class);
    }
    // scopes
    function scopePending($query)
    {
        return $query->where("status", 0);
    }
    function scopeReviewed($query, $value)
    {
        return $query->where("is_reviewed", $value);
    }
    function scopeCompleted($query)
    {
        return $query->where("status", 3);
    }

    function hourly_slots(){
        return $this->hasMany(BookingHourSlot::class);
    }
    function tour_durations(){
        return $this->hasMany(BookingTourDuration::class);
    }
}
